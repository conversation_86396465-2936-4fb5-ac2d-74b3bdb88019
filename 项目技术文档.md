# Infer-Retrieve-Rank (IReRa) 项目技术文档

## 项目概述

Infer-Retrieve-Rank (IReRa) 是一个基于DSPy框架的极端多标签分类系统，专门用于处理具有大量类别（≥10,000个类别）的分类任务。该系统通过预训练语言模型和检索器的交互，仅使用约50个标注样本就能实现最先进的性能。

### 核心特性
- **模块化设计**：推理(Infer)、检索(Retrieve)、排序(Rank)三个独立模块
- **教师-学生优化**：使用强大的教师模型指导高效的学生模型
- **先验概率调整**：基于标签频率的分数重新加权机制
- **多数据集支持**：ESCO、BioDEX、Math1等多个数据集

## 系统架构

### 1. 核心模块结构

```
InferRetrieveRank
├── Chunker (文本分块)
├── InferRetrieve
│   ├── Infer (推理模块)
│   └── Retriever (检索模块)
└── Rank (排序模块)
```

### 2. 数据流程

1. **文本分块** → 2. **推理生成查询** → 3. **向量检索** → 4. **先验调整** → 5. **重新排序** → 6. **输出预测**

## 详细模块分析

### 1. 配置管理 (IreraConfig)

**文件位置**: `src/programs/config.py`

配置类管理所有系统参数，包括：

```python
class IreraConfig:
    def __init__(self, **kwargs):
        # 签名配置
        self.infer_signature_name = kwargs.pop("infer_signature_name")
        self.rank_signature_name = kwargs.pop("rank_signature_name")
        
        # 超参数
        self.prior_A = kwargs.pop("prior_A", 0)  # 先验调整强度
        self.rank_topk = kwargs.pop("rank_topk", 50)  # 排序候选数量
        
        # 分块参数
        self.chunk_context_window = kwargs.pop("chunk_context_window", 3000)
        self.chunk_max_windows = kwargs.pop("chunk_max_windows", 5)
        
        # 模型配置
        self.retriever_model_name = kwargs.pop("retriever_model_name", 
            "/home/<USER>/ZhouSQ/DCX/xmc.dspy/all-mpnet-base-v2")
```

**关键参数说明**：
- `prior_A`: 先验概率调整参数，控制标签频率对分数的影响程度
- `rank_topk`: 从检索结果中选择前K个候选进行重新排序
- `chunk_context_window`: 文本分块的窗口大小

### 2. 文本分块模块 (Chunker)

**文件位置**: `src/programs/chunking.py`

负责将长文本分割成可处理的块：

```python
class Chunker:
    def __call__(self, text):
        snippet_idx = 0
        while snippet_idx < self.chunk_max_windows and text:
            # 计算结束位置，包含重叠
            endpos = int(self.chunk_context_window * (1.0 + self.chunk_window_overlap))
            snippet, text = text[:endpos], text[endpos:]
            
            # 在换行符处分割，避免截断句子
            next_newline_pos = snippet.rfind("\n")
            if (text and next_newline_pos != -1 and 
                next_newline_pos >= self.chunk_context_window // 2):
                text = snippet[next_newline_pos + 1:] + text
                snippet = snippet[:next_newline_pos]
            
            yield snippet_idx, snippet.strip()
            snippet_idx += 1
```

**实现细节**：
- 支持重叠分块，避免信息丢失
- 在换行符处智能分割，保持语义完整性
- 限制最大分块数量，控制计算复杂度

### 3. 推理模块 (Infer)

**文件位置**: `src/programs/infer.py`

使用语言模型从输入文本生成查询标签：

```python
class Infer(dspy.Module):
    def __init__(self, config: IreraConfig):
        super().__init__()
        self.config = config
        self.cot = dspy.ChainOfThought(
            supported_signatures[config.infer_signature_name]
        )

    def forward(self, text: str) -> dspy.Prediction:
        parsed_outputs = set()
        
        # 使用思维链推理生成输出
        output = self.cot(text=text).completions.output
        
        # 解析输出标签
        parsed_outputs.update(
            extract_labels_from_strings(output, do_lower=False, strip_punct=False)
        )
        
        return dspy.Prediction(predictions=parsed_outputs)
```

**核心功能**：
- 使用DSPy的ChainOfThought进行推理
- 支持不同领域的签名（ESCO、BioDEX、Math1）
- 输出标准化处理

### 4. 检索模块 (Retriever)

**文件位置**: `src/programs/retriever.py`

基于语义相似度检索相关标签：

```python
class Retriever:
    def __init__(self, config: IreraConfig):
        # 初始化句子变换器模型
        self.model = SentenceTransformer(self.retriever_model_name, device="cuda:3")
        
        # 加载本体术语和嵌入
        self.ontology_terms = self._load_terms()
        self.ontology_embeddings = self._load_embeddings()

    def _load_embeddings(self) -> torch.Tensor:
        """加载或创建所有查询术语的嵌入"""
        embedding_dir = os.path.join('.', 'data', 'embeddings')
        ontology_embeddings_filename = os.path.join(
            embedding_dir, f"{self.ontology_name}_embeddings[{self.friendly_model_name}].pt"
        )
        
        # 如果文件存在则加载，否则创建嵌入
        if os.path.isfile(ontology_embeddings_filename):
            with open(ontology_embeddings_filename, "rb") as f:
                ontology_embeddings = torch.load(f, map_location=torch.device("cpu"))
        else:
            self.model.to(torch.device("cuda" if torch.cuda.is_available() else "cpu"))
            ontology_embeddings = self.model.encode(
                self.ontology_terms, convert_to_tensor=True, show_progress_bar=True
            )
            with open(ontology_embeddings_filename, "wb") as f:
                torch.save(ontology_embeddings, f)
            self.model.to(torch.device("cpu"))
        return ontology_embeddings

    def retrieve(self, queries: set[str]) -> dict[str, float]:
        """对每个本体标签，获取所有查询的最大相似度"""
        queries = list(queries)
        
        # 获取每个查询的相似度
        query_embeddings = self.model.encode(queries, convert_to_tensor=True)
        query_results = sentence_transformers.util.semantic_search(
            query_embeddings, self.ontology_embeddings,
            query_chunk_size=64, top_k=len(self.ontology_embeddings)
        )
        
        # 重新格式化结果为 查询 --> [分数] 映射
        query_results_reformat = defaultdict(list)
        for query, query_result in zip(queries, query_results):
            for r in query_result:
                query = self.ontology_terms[r["corpus_id"]]
                query_score = r["score"]
                query_results_reformat[query].append(query_score)
        
        # 对每个查询获取最大分数
        query_to_score = {k: max(v) for k, v in query_results_reformat.items()}
        
        return query_to_score
```

**技术特点**：
- 使用all-mpnet-base-v2作为默认嵌入模型
- 支持嵌入缓存，避免重复计算
- 基于余弦相似度的语义搜索
- LRU缓存优化频繁查询

### 5. 推理-检索模块 (InferRetrieve)

**文件位置**: `src/programs/infer_retrieve.py`

结合推理和检索，并应用先验概率调整：

```python
class InferRetrieve(dspy.Module):
    def forward(self, text: str) -> dspy.Prediction:
        # 使用语言模型预测每个块的标签查询
        preds = self.infer(text).predictions
        
        # 对标签索引执行查询并获取每个标签的最大分数
        scores = self.retriever.retrieve(preds)
        
        # 使用先验统计重新加权分数
        scores = self._update_scores_with_prior(scores)
        
        # 返回排序后的标签
        labels = sorted(scores, key=lambda k: scores[k], reverse=True)
        
        return dspy.Prediction(predictions=labels)

    def _update_scores_with_prior(self, scores: dict[str, float]) -> dict[str, float]:
        """应用先验概率调整公式"""
        scores = {
            label: score * math.log(self.prior_A * self.prior[label] + math.e)
            for label, score in scores.items()
        }
        return scores
```

**先验概率调整机制**：
- 公式：`adjusted_score = original_score * log(A * prior_probability + e)`
- 高频标签获得更高权重
- 参数A控制调整强度

### 6. 排序模块 (Rank)

**文件位置**: `src/programs/rank.py`

对候选标签进行精细排序：

```python
class Rank(dspy.Module):
    def forward(self, text: str, options: list[str]) -> dspy.Predict:
        parsed_outputs = []
        
        # 使用思维链推理对选项进行排序
        output = self.cot(text=text, options=options).completions.output
        
        # 解析输出标签
        parsed_outputs = extract_labels_from_strings(
            output, do_lower=False, strip_punct=False, split_colon=True
        )
        
        return dspy.Prediction(predictions=parsed_outputs)
```

**排序策略**：
- 基于上下文的精确匹配
- 支持领域特定的排序签名
- 输出验证和过滤

## 数据集构建流程

### 1. 数据加载器架构

**文件位置**: `src/data_loaders/loader.py`

统一的数据加载接口：

```python
def load_data(dataset: str):
    if dataset == "esco_tech":
        validation_df, test_df, ontology_items, ontology_descriptions, ontology_prior = _load_esco(
            "tech", "tech_validation_annotations.csv", "tech_test_annotations.csv"
        )
    elif dataset == "biodex_reactions":
        validation_df, test_df, ontology_items, ontology_descriptions, ontology_prior = _load_biodex()
    elif dataset == "math1":
        validation_df, test_df, ontology_items, ontology_descriptions, ontology_prior = _load_math1()
    
    # 转换为DSPy示例格式
    validation_examples, test_examples = get_dspy_examples(validation_df, test_df)
    
    # 数据集特定的训练/验证分割
    if dataset == "math1":
        train_examples = validation_examples[:10]
        validation_examples = validation_examples[10:60]
        test_examples = test_examples[:250]
    
    return train_examples, validation_examples, test_examples, ontology_items, ontology_descriptions, ontology_prior
```

### 2. Math1数据集处理

**文件位置**: `src/data_loaders/math1.py`

专门处理数学概念分类数据：

```python
def _prepare_math1_dataframe(file_path):
    """将math1数据转换为项目所需的DataFrame格式"""
    with open(file_path, 'r') as f:
        data = json.load(f)
    
    texts = []
    labels = []
    
    for item in data:
        texts.append(item["doc_token"])  # 数学题目文本
        labels.append(item["doc_label"])  # 数学概念标签列表
    
    df = pd.DataFrame({"text": texts, "label": labels})
    return df

def _load_math1_ontology(math1_dir):
    """加载math1数据的本体（标签集合）"""
    train_file = os.path.join(math1_dir, "train.json")
    with open(train_file, 'r') as f:
        train_data = json.load(f)
    
    # 收集所有唯一标签
    all_labels = set()
    for item in train_data:
        all_labels.update(item["doc_label"])
    
    ontology_items = list(all_labels)
    
    # 创建先验概率（基于训练数据中的频率）
    label_counter = Counter()
    total_samples = len(train_data)
    
    for item in train_data:
        for label in item["doc_label"]:
            label_counter[label] += 1
    
    # 计算先验概率
    ontology_priors = defaultdict(
        lambda: 0.0,
        {k: v / total_samples for k, v in label_counter.items()}
    )
    
    return ontology_items, None, ontology_priors
```

### 3. 先验概率计算

**文件位置**: `scripts/prepare_math1_data.py`

自动化先验概率文件生成：

```python
def apply_prior_adjustment(original_score, prior_prob, A=1.0):
    """
    应用先验概率调整公式: ŝᵢ = sᵢ · log₁₀(A · pᵢ + 10)
    
    Args:
        original_score: 原始分数 sᵢ
        prior_prob: 先验概率 pᵢ  
        A: 调整参数，默认为1.0
    
    Returns:
        调整后的分数 ŝᵢ
    """
    if prior_prob <= 0:
        prior_prob = 1e-6  # 避免log(0)
    
    adjustment_factor = math.log10(A * prior_prob + 10)
    adjusted_score = original_score * adjustment_factor
    
    return adjusted_score
```

**先验概率调整效果**：
- 高频标签（如"小数的认识"）：分数提升0.34%
- 低频标签（如"初识1-10"）：分数基本不变
- 调整幅度通常在0.01%-0.35%之间

## 优化策略

### 1. Left-to-Right优化器

**文件位置**: `src/optimizer.py`

分阶段优化策略：

```python
class LeftToRightOptimizer:
    def optimize(self, program, train_examples, validation_examples):
        # 第一轮：优化推理模块
        if self.infer_compile:
            # 创建教师模型
            teacher = InferRetrieveRank(program.config)
            teacher.infer_retrieve.infer.cot.lm = self.modules_to_lms["infer_retrieve.infer"]["teacher"]
            
            # 第一轮不使用排序
            teacher.rank_skip = True
            program.rank_skip = True
            
            # 创建编译器
            infer_compiler = self.create_compiler(self.infer_compile_metric)
            
            # 编译推理模块
            program = infer_compiler.compile(
                program, teacher=teacher,
                trainset=train_examples, valset=validation_examples,
                restrict=range(20)
            )
            
            # 恢复排序模块
            program.rank_skip = rank_skipped
        
        # 第二轮：优化排序模块
        if self.rank_compile and not program.rank_skip:
            teacher = program.deepcopy()
            teacher.rank.cot.lm = self.modules_to_lms["rank"]["teacher"]
            
            rank_compiler = self.create_compiler(self.rank_compile_metric)
            
            program = rank_compiler.compile(
                program, teacher=teacher,
                trainset=train_examples, valset=validation_examples,
                restrict=range(20)
            )
        
        return program
```

**优化参数**：
- `max_bootstrapped_demos`: 2 (自举演示数量)
- `max_rounds`: 1 (优化轮数)
- `num_candidate_programs`: 10 (候选程序数量)

### 2. 教师-学生模型配置

典型配置示例：
- **教师模型**: GPT-4-1106-preview (高质量推理)
- **学生模型**: Llama-2-13b-chat (高效执行)
- **检索模型**: all-mpnet-base-v2 (语义嵌入)

## 评估指标

### 1. 核心指标定义

**文件位置**: `src/metrics.py`

```python
def rp_at_k(gold: list, predicted: list, k: int):
    """
    计算Rank Precision at K (RP@K)
    
    RP@K = (前K个预测中的正确数量) / min(K, 真实标签数量)
    """
    gold_k = min(k, len(gold))
    top_k_predicted = predicted[:k]
    true_positives = sum(1 for item in top_k_predicted if item in gold)
    rp_at_k = true_positives / gold_k if gold_k > 0 else 0.0
    return rp_at_k

def recall_at_k(gold: list, predicted: list, k: int):
    """
    计算Recall at K (Recall@K)
    
    Recall@K = (前K个预测中的正确数量) / 真实标签总数
    """
    rank = [x in gold for x in predicted]
    recall = sum(rank[:k]) / len(gold)
    return recall
```

### 2. 评估器封装

**文件位置**: `src/evaluators.py`

```python
def create_evaluators(examples):
    """创建基于示例集的DSPy评估器套件"""
    return {
        "recall10": Evaluate(devset=examples, metric=dspy_metric_recall10, num_threads=num_threads),
        "rp50": Evaluate(devset=examples, metric=dspy_metric_rp50, num_threads=num_threads),
        "rp10": Evaluate(devset=examples, metric=dspy_metric_rp10, num_threads=num_threads),
        "rp5": Evaluate(devset=examples, metric=dspy_metric_rp5, num_threads=num_threads),
    }
```

## 实验管理

### 1. 实验配置跟踪

**文件位置**: `src/experiment.py`

```python
@dataclass
class Experiment:
    """跟踪实验配置、优化参数和结果程序状态"""
    
    # 实验配置
    dataset_name: str
    program_name: str
    infer_student_model_name: str
    infer_teacher_model_name: str
    
    # 结果
    validation_rp5: float = None
    validation_rp10: float = None
    test_rp5: float = None
    test_rp10: float = None
    
    # 程序状态
    program_state: dict = None
    
    def save(self, results_dir: str):
        """保存实验结果和程序状态"""
        # 自动生成唯一实验名称
        name = f"{self.dataset_name}_{self.program_name}_{index:02d}"
        
        # 分别保存结果和程序状态
        results_file = path.join(file, "results.json")
        state_file = path.join(file, "program_state.json")
```

## 运行流程

### 1. 编译流程

**主文件**: `compile_irera.py`

```bash
python compile_irera.py \
    --dataset_name esco_tech \
    --infer_signature_name infer_esco \
    --rank_signature_name rank_esco \
    --infer_student_model_name llama-2-13b-chat \
    --infer_teacher_model_name gpt-3.5-turbo-instruct \
    --rank_student_model_name gpt-4-1106-preview \
    --rank_teacher_model_name gpt-4-1106-preview \
    --optimizer_name left-to-right \
    --do_validation --do_test
```

### 2. 推理流程

**主文件**: `run_irera.py`

```bash
python run_irera.py \
    --dataset_name esco_tech \
    --state_path ./results_precompiled/esco_tech_infer-retrieve-rank_00/program_state.json \
    --do_validation --do_test
```

## 工具函数

### 1. 标签处理

**文件位置**: `src/utils.py`

```python
def normalize(label: str, do_lower: bool = True, strip_punct: bool = True, split_colon: bool = False) -> str:
    """标准化标签文本"""
    # 移除字段前缀
    if split_colon:
        label = label.split(":")[1] if ":" in label else label
    
    # 移除前后换行符
    label = label.strip("\n")
    
    # 移除前后标点符号
    if strip_punct:
        label = re.sub(r"^[^\w\s]+|[^\w\s]+$", "", label, flags=re.UNICODE)
    
    # 转换为小写
    if do_lower:
        return label.strip().lower()
    else:
        return label.strip()

def extract_labels_from_strings(labels: list[str], do_lower: bool = True, strip_punct: bool = True, split_colon: bool = False) -> list[str]:
    """从字符串列表中提取标签"""
    labels = [normalize(r, do_lower=do_lower, strip_punct=strip_punct, split_colon=split_colon) for r in labels]
    labels = ", ".join(labels)
    return extract_labels_from_string(labels, do_lower=do_lower, strip_punct=strip_punct, split_colon=split_colon)
```

## 总结

IReRa系统通过模块化设计实现了高效的极端多标签分类，其核心创新包括：

1. **三阶段流水线**：推理→检索→排序的清晰分工
2. **先验概率调整**：基于标签频率的智能重新加权
3. **教师-学生优化**：平衡性能和效率的训练策略
4. **多数据集支持**：灵活的数据加载和处理框架

该系统在ESCO、BioDEX等多个数据集上取得了最先进的性能，证明了其在实际应用中的有效性。

## 签名系统详解

### 1. 签名架构

**文件位置**: `src/programs/signatures.py`

签名定义了语言模型的任务描述和输入输出格式：

```python
class InferSignatureESCO(dspy.Signature):
    """给定职位空缺片段，识别所有提到的ESCO职业技能。始终返回技能。"""

    text = dspy.InputField(prefix="Vacancy:")
    output = dspy.OutputField(
        prefix="Skills:",
        desc="逗号分隔的ESCO技能列表",
        format=lambda x: ", ".join(x) if isinstance(x, list) else x,
    )

class RankSignatureESCO(dspy.Signature):
    """给定职位空缺片段，从选项中选择10个最适用的技能。"""

    text = dspy.InputField(prefix="Vacancy:")
    options = dspy.InputField(
        prefix="Options:",
        desc="可选择的逗号分隔选项列表",
        format=lambda x: ", ".join(x) if isinstance(x, list) else x,
    )
    output = dspy.OutputField(
        prefix="Skills:",
        desc="逗号分隔的ESCO技能列表",
        format=lambda x: ", ".join(x) if isinstance(x, list) else x,
    )
```

### 2. 领域特定签名

**Math1数学概念签名**：
```python
class InferSignatureMath1(dspy.Signature):
    """给定数学应用题，识别相关的数学概念和技能。始终返回概念。"""

    text = dspy.InputField(prefix="Problem:")
    output = dspy.OutputField(
        prefix="Concepts:",
        desc="逗号分隔的数学概念列表",
        format=lambda x: ", ".join(x) if isinstance(x, list) else x,
    )
```

**BioDEX医学反应签名**：
```python
class InferSignatureBioDEX(dspy.Signature):
    """给定医学文章片段，识别影响患者的药物不良反应。始终返回反应。"""

    text = dspy.InputField(prefix="Article:")
    output = dspy.OutputField(
        prefix="Reactions:",
        desc="逗号分隔的药物不良反应列表",
        format=lambda x: ", ".join(x) if isinstance(x, list) else x,
    )
```

### 3. 签名注册机制

```python
supported_signatures = {
    "infer_esco": InferSignatureESCO,
    "rank_esco": RankSignatureESCO,
    "infer_biodex": InferSignatureBioDEX,
    "rank_biodex": RankSignatureBioDEX,
    "infer_math1": InferSignatureMath1,
    "rank_math1": RankSignatureMath1,
}
```

## 数据集详细分析

### 1. ESCO数据集处理

**文件位置**: `src/data_loaders/esco.py`

ESCO (European Skills, Competences, Qualifications and Occupations) 数据集处理：

```python
def _load_esco_ontology(esco_dir):
    """加载ESCO本体和先验概率"""
    # 读取技能标签文件
    ontology_file = os.path.join(esco_dir, "skills_en_label.csv")
    ontology_prior = os.path.join(esco_dir, "esco_priors.json")

    # 获取技能和描述
    ontology = pd.read_csv(ontology_file)
    skills_and_description = ontology[["preferredLabel", "description"]].to_numpy()
    skills = [x[0] for x in skills_and_description]
    descriptions = [x[1] for x in skills_and_description]

    # 获取先验概率
    with open(ontology_prior, "r") as f:
        priors = defaultdict(lambda: 0.0)
        priors.update(json.load(f))

    return skills, descriptions, priors

def _load_esco(subset_name, validation_file, test_file):
    """加载ESCO数据集的特定子集"""
    base_dir = "./data"
    esco_dir = os.path.join(base_dir, "esco")

    # 加载本体
    skills, descriptions, priors = _load_esco_ontology(esco_dir)

    # 加载验证和测试数据
    if validation_file:
        validation_df = pd.read_csv(os.path.join(esco_dir, validation_file))
    else:
        validation_df = None

    test_df = pd.read_csv(os.path.join(esco_dir, test_file))

    return validation_df, test_df, skills, descriptions, priors
```

**ESCO数据集特点**：
- **esco_tech**: 技术职位技能分类
- **esco_house**: 家政服务技能分类
- **esco_techwolf**: TechWolf公司特定技能分类
- 包含技能描述和层次结构信息

### 2. BioDEX数据集处理

**文件位置**: `src/data_loaders/biodex.py`

生物医学药物不良反应数据集：

```python
def _prepare_biodex_dataframe(dataset):
    """准备BioDEX数据框架"""
    label = [
        extract_labels_from_string(
            l, do_lower=False, strip_punct=False,
        )
        for l in dataset["reactions"]
    ]
    df = pd.DataFrame({"text": dataset["fulltext_processed"], "label": label})
    return df

def _load_biodex():
    """加载BioDEX数据集"""
    base_dir = "./data"
    biodex_dir = os.path.join(base_dir, "biodex")

    # 获取本体术语
    biodex_terms = [
        term.strip("\n")
        for term in open(os.path.join(biodex_dir, "reaction_terms.txt")).readlines()
    ]

    # 获取验证和测试集
    dataset = datasets.load_dataset("BioDEX/BioDEX-Reactions")
    validation_ds, test_ds = dataset["validation"], dataset["test"]

    # 从训练集获取先验计数
    all_train_reactions = dataset["train"]["reactions"]
    all_train_reactions = [ls.split(", ") for ls in all_train_reactions]
    all_train_reactions = [x for ls in all_train_reactions for x in ls]

    biodex_priors = Counter(all_train_reactions)
    biodex_priors = defaultdict(
        lambda: 0.0,
        {k: v / len(all_train_reactions) for k, v in biodex_priors.items()},
    )

    # 保存先验概率
    with open(os.path.join(biodex_dir, "biodex_priors.json"), "w") as fp:
        json.dump(biodex_priors, fp)

    # 获取正确格式的数据框
    validation_df = _prepare_biodex_dataframe(validation_ds)
    test_df = _prepare_biodex_dataframe(test_ds)

    return validation_df, test_df, biodex_terms, None, biodex_priors
```

**BioDEX数据集特点**：
- 医学文献中的药物不良反应识别
- 使用HuggingFace datasets库加载
- 自动计算和保存先验概率

### 3. Math1数据集详细处理

**先验概率生成脚本分析**：

```python
def generate_prior_probabilities_for_points():
    """为math1_points.txt中的每个标签生成对应的先验概率"""
    data_dir = "/home/<USER>/ZhouSQ/DCX/xmc.dspy/data/math1_data"

    # 读取math1_points.txt中的标签
    points_file = os.path.join(data_dir, "math1_points.txt")
    with open(points_file, 'r', encoding='utf-8') as f:
        point_labels = [line.strip() for line in f if line.strip()]

    # 读取现有的先验概率
    priors_file = os.path.join(data_dir, "math1_priors.json")
    with open(priors_file, 'r', encoding='utf-8') as f:
        all_priors = json.load(f)

    # 为每个标签生成先验概率
    point_priors = {}
    missing_labels = []

    for label in point_labels:
        if label in all_priors:
            point_priors[label] = all_priors[label]
        else:
            # 如果标签不在训练数据中，设置一个很小的默认先验概率
            point_priors[label] = 1e-6
            missing_labels.append(label)

    # 保存math1_points的先验概率
    points_priors_file = os.path.join(data_dir, "math1_points_priors.json")
    with open(points_priors_file, 'w', encoding='utf-8') as f:
        json.dump(point_priors, f, ensure_ascii=False, indent=2)

    print(f"生成了 {len(point_labels)} 个标签的先验概率")
    print(f"其中 {len(point_labels) - len(missing_labels)} 个标签找到了先验概率")
    print(f"有 {len(missing_labels)} 个标签使用默认先验概率 1e-6")
```

**先验概率调整示例**：

```python
def create_score_adjustment_example():
    """创建一个完整的分数调整示例"""
    # 示例分数
    example_scores = {
        "小数的认识": 0.8,
        "分数的意义": 0.7,
        "初识1-10": 0.6
    }

    print("先验概率调整示例:")
    print(f"{'标签':<15} {'原始分数':<10} {'先验概率':<12} {'调整后分数':<12} {'变化率':<10}")
    print("-" * 70)

    for label, original_score in example_scores.items():
        if label in point_priors:
            prior_prob = point_priors[label]
            adjusted_score = apply_prior_adjustment(original_score, prior_prob, A=1.0)
            change_rate = ((adjusted_score - original_score) / original_score) * 100

            print(f"{label:<15} {original_score:<10.3f} {prior_prob:<12.6f} {adjusted_score:<12.6f} {change_rate:<10.4f}%")
```

## 脚本系统分析

### 1. 编译脚本

**Left-to-Right编译** (`scripts/compile_left_to_right.sh`):
```bash
# ESCO_tech数据集编译
python compile_irera.py \
    --lm_config_path ./lm_config.json \
    --retriever_model_name /home/<USER>/ZhouSQ/DCX/xmc.dspy/all-mpnet-base-v2 \
    --dataset_name esco_tech \
    --infer_signature_name infer_esco \
    --rank_signature_name rank_esco \
    --infer_student_model_name llama-2-13b-chat \
    --infer_teacher_model_name gpt-3.5-turbo-instruct \
    --rank_student_model_name gpt-4-1106-preview \
    --rank_teacher_model_name gpt-4-1106-preview \
    --infer_compile_metric_name rp10 \
    --rank_compile_metric_name rp10 \
    --prior_A 0 \
    --rank_topk 50 \
    --do_validation --do_test \
    --prior_path ./data/esco/esco_priors.json \
    --ontology_path ./data/esco/skills_en_label.txt \
    --ontology_name esco \
    --optimizer_name left-to-right
```

**Math1专用编译脚本** (`scripts/compile_math1.sh`):
```bash
python compile_irera.py \
    --dataset_name math1 \
    --ontology_name math1_data \
    --prior_path /home/<USER>/ZhouSQ/DCX/xmc.dspy/data/math1_data/math1_points_priors.json \
    --ontology_path /home/<USER>/ZhouSQ/DCX/xmc.dspy/data/math1_data/math1_points.txt \
    --infer_signature_name infer_math1 \
    --rank_signature_name rank_math1 \
    --retriever_model_name /home/<USER>/ZhouSQ/DCX/xmc.dspy/all-mpnet-base-v2 \
    --infer_student_model_name qwen3-0.6b \
    --infer_teacher_model_name qwen3-0.6b \
    --rank_student_model_name qwen3-0.6b \
    --rank_teacher_model_name qwen3-0.6b \
    --prior_A 0 \
    --optimizer_name left-to-right
```

### 2. 数据加载脚本

**数据下载脚本** (`scripts/load_data.sh`):
```bash
# 创建ESCO数据目录
mkdir -p data/esco
cd data/esco

# 克隆技能提取基准数据集
git clone https://github.com/jensjorisdecorte/Skill-Extraction-benchmark.git
cd Skill-Extraction-benchmark
git checkout 157da05a24e6ecfee82e4b5d01cba68a2ed0552f

# 移动文件到上级目录
mv * ..
cd ../../..

# 清理临时目录
rm -rf data/esco/Skill-Extraction-benchmark/
```

**缓存管理脚本**:
- `scripts/load_cache.sh`: 解压预计算的模型调用缓存
- `scripts/save_cache.sh`: 压缩并保存当前缓存

### 3. 测试脚本

**先验概率调整测试** (`scripts/test_prior_adjustment.py`):
```python
def test_basic_functionality():
    """测试基本功能"""
    test_cases = [
        (0.5, 0.1, 1.0),    # 中等分数，高先验概率
        (0.8, 0.001, 1.0),  # 高分数，低先验概率
        (0.3, 0.05, 1.0),   # 低分数，中等先验概率
    ]

    for original, prior, A in test_cases:
        adjusted = apply_prior_adjustment(original, prior, A)
        change_rate = ((adjusted - original) / original) * 100
        print(f"{original:<10.3f} {prior:<12.6f} {adjusted:<12.6f} {change_rate:<10.4f}%")

def test_different_A_values():
    """测试不同的A参数值"""
    test_score = 0.5
    test_prior = 0.01
    A_values = [0.1, 0.5, 1.0, 2.0, 5.0]

    for A in A_values:
        adjusted = apply_prior_adjustment(test_score, test_prior, A)
        change_rate = ((adjusted - test_score) / test_score) * 100
        print(f"{A:<8.1f} {adjusted:<12.6f} {change_rate:<10.4f}%")
```

## 性能优化技术

### 1. 缓存机制

**LRU缓存优化**:
```python
@lru_cache(maxsize=100000)
def retrieve_individual(self, query: str, K: int = 3) -> list[tuple[float, str]]:
    """使用LRU缓存优化频繁查询"""
    query_embeddings = self.model.encode(query, convert_to_tensor=True)
    # ... 检索逻辑
```

**嵌入缓存**:
```python
def _load_embeddings(self) -> torch.Tensor:
    """加载或创建所有查询术语的嵌入"""
    ontology_embeddings_filename = os.path.join(
        embedding_dir, f"{self.ontology_name}_embeddings[{self.friendly_model_name}].pt"
    )

    # 如果文件存在则加载，否则创建嵌入
    if os.path.isfile(ontology_embeddings_filename):
        with open(ontology_embeddings_filename, "rb") as f:
            ontology_embeddings = torch.load(f, map_location=torch.device("cpu"))
    else:
        # 创建新嵌入并保存
        ontology_embeddings = self.model.encode(
            self.ontology_terms, convert_to_tensor=True, show_progress_bar=True
        )
        with open(ontology_embeddings_filename, "wb") as f:
            torch.save(ontology_embeddings, f)
```

### 2. 内存管理

**GPU内存优化**:
```python
# 避免深拷贝以节省GPU内存
teacher = InferRetrieveRank(program.config)
teacher.infer_retrieve.infer.cot.lm = self.modules_to_lms["infer_retrieve.infer"]["teacher"]

# 模型设备管理
self.model.to(torch.device("cuda" if torch.cuda.is_available() else "cpu"))
# 计算完成后移回CPU
self.model.to(torch.device("cpu"))
```

**多线程支持**:
```python
# 环境变量控制线程数
num_threads = os.environ.get('DSP_NUM_THREADS', 1)

# 评估器中使用多线程
evaluate_rp10 = Evaluate(
    devset=examples,
    metric=dspy_metric_rp10,
    num_threads=num_threads,
    display_progress=False,
    max_errors=100,
)
```

### 3. 批处理优化

**批量语义搜索**:
```python
def retrieve(self, queries: set[str]) -> dict[str, float]:
    """批量处理查询以提高效率"""
    queries = list(queries)

    # 批量编码查询
    query_embeddings = self.model.encode(queries, convert_to_tensor=True)

    # 批量语义搜索
    query_results = sentence_transformers.util.semantic_search(
        query_embeddings, self.ontology_embeddings,
        query_chunk_size=64,  # 分块处理大批量
        top_k=len(self.ontology_embeddings),
    )
```

## 错误处理和调试

### 1. 异常处理

**数据加载错误处理**:
```python
def load_data(dataset: str):
    try:
        if dataset == "esco_tech":
            return _load_esco("tech", "tech_validation_annotations.csv", "tech_test_annotations.csv")
        elif dataset == "biodex_reactions":
            return _load_biodex()
        elif dataset == "math1":
            return _load_math1()
        else:
            raise ValueError(f"Dataset {dataset} not supported.")
    except FileNotFoundError as e:
        print(f"数据文件未找到: {e}")
        raise
    except Exception as e:
        print(f"数据加载失败: {e}")
        raise
```

### 2. 调试工具

**详细日志输出**:
```python
# 数据集统计信息
print(f"Dataset: {dataset}")
print(f"# {dataset}: Total Validation size: {len(validation_examples)}")
print(f"# {dataset}: Total Test size: {len(test_examples)}")
print(f'{dataset}: avg # ontology items per input: {round(validation_df["label"].apply(len).mean(),2)}')

# 优化过程跟踪
print("validating final program...")
print("Final program validation_rp50: ", validation_rp50)
print("Final program validation_rp10: ", validation_rp10)
```

**程序状态保存**:
```python
def dump_state(self):
    """转储状态，包含DSPy状态和配置文件"""
    return super().dump_state() | {"config": self.config.to_dict()}

def save(self, path: str):
    """保存程序状态到文件"""
    state = self.dump_state()
    with open(path, "w") as fp:
        json.dump(state, fp)
```

## 扩展性设计

### 1. 新数据集集成

添加新数据集的步骤：

1. **创建数据加载器** (`src/data_loaders/new_dataset.py`):
```python
def _load_new_dataset():
    """加载新数据集"""
    # 实现数据加载逻辑
    return validation_df, test_df, ontology_items, ontology_descriptions, ontology_prior
```

2. **注册数据加载器** (`src/data_loaders/loader.py`):
```python
def load_data(dataset: str):
    if dataset == "new_dataset":
        return _load_new_dataset()
    # ... 其他数据集
```

3. **定义领域签名** (`src/programs/signatures.py`):
```python
class InferSignatureNewDataset(dspy.Signature):
    """新数据集的推理签名"""
    text = dspy.InputField(prefix="Input:")
    output = dspy.OutputField(prefix="Output:", desc="输出描述")

supported_signatures["infer_new_dataset"] = InferSignatureNewDataset
```

### 2. 新优化器集成

```python
class CustomOptimizer:
    """自定义优化器"""
    def __init__(self, modules_to_lms, **kwargs):
        self.modules_to_lms = modules_to_lms
        # 初始化优化器特定参数

    def optimize(self, program, train_examples, validation_examples):
        # 实现自定义优化逻辑
        return optimized_program

# 注册新优化器
supported_optimizers["custom"] = CustomOptimizer
```

### 3. 新评估指标

```python
def custom_metric(gold: list, predicted: list, k: int):
    """自定义评估指标"""
    # 实现评估逻辑
    return score

def dspy_metric_custom(gold: Example, pred, trace=None) -> float:
    """DSPy包装的自定义指标"""
    return custom_metric(gold.label, pred.predictions, k=10)

# 注册新指标
supported_metrics["custom"] = dspy_metric_custom
```

## 完整执行流程分析

### 1. 编译阶段详细流程

**主函数执行路径** (`compile_irera.py`):

```python
def compile_irera(dataset_name, retriever_model_name, infer_signature_name, ...):
    # 步骤1: 创建配置对象
    config = IreraConfig(
        infer_signature_name=infer_signature_name,
        rank_signature_name=rank_signature_name,
        prior_A=prior_A,
        prior_path=prior_path,
        rank_topk=rank_topk,
        rank_skip=rank_skip,
        ontology_path=ontology_path,
        ontology_name=ontology_name,
        retriever_model_name=retriever_model_name,
        optimizer_name=optimizer_name,
    )

    # 步骤2: 加载数据集
    (train_examples, validation_examples, test_examples,
     ontology_items, ontology_descriptions, ontology_prior) = load_data(dataset_name)

    # 步骤3: 创建程序实例
    program = InferRetrieveRank(config)

    # 步骤4: 配置教师-学生模型映射
    modules_to_lms = {
        "infer_retrieve.infer": {
            "teacher": Models.get_lm(infer_teacher_model_name),
            "student": Models.get_lm(infer_student_model_name),
        },
        "rank": {
            "teacher": Models.get_lm(rank_teacher_model_name),
            "student": Models.get_lm(rank_student_model_name),
        },
    }

    # 步骤5: 设置学生模型
    program.infer_retrieve.infer.cot.lm = modules_to_lms["infer_retrieve.infer"]["student"]
    program.rank.cot.lm = modules_to_lms["rank"]["student"]

    # 步骤6: 创建并执行优化器
    optimizer_class = supported_optimizers[config.optimizer_name]
    optimizer = optimizer_class(
        modules_to_lms=modules_to_lms,
        infer_compile=infer_compile,
        infer_compile_metric_name=infer_compile_metric_name,
        rank_compile=rank_compile,
        rank_compile_metric_name=rank_compile_metric_name,
    )

    # 步骤7: 执行优化
    program = optimizer.optimize(program, train_examples, validation_examples)

    # 步骤8: 评估性能
    if do_validation:
        validation_evaluators = create_evaluators(validation_examples)
        validation_rp50 = validation_evaluators["rp50"](program)
        validation_rp10 = validation_evaluators["rp10"](program)
        validation_rp5 = validation_evaluators["rp5"](program)

    # 步骤9: 保存实验结果
    exp = Experiment(
        dataset_name=dataset_name,
        program_name="infer-retrieve-rank",
        infer_student_model_name=infer_student_model_name,
        infer_teacher_model_name=infer_teacher_model_name,
        validation_rp5=validation_rp5,
        validation_rp10=validation_rp10,
        program_state=program.dump_state(),
    )
    exp.save("./results")

    return exp, program
```

### 2. 推理阶段详细流程

**单个样本处理流程**:

```python
def forward(self, text: str) -> dspy.Prediction:
    # 步骤1: 文本分块
    _, text = next(self.chunker(text))

    # 步骤2: 推理-检索流水线
    prediction = self.infer_retrieve(text)
    labels = prediction.predictions

    # 步骤3: 获取排序候选
    options = labels[:self.rank_topk]  # 取前50个候选

    # 步骤4: 重新排序（如果启用）
    if not self.rank_skip:
        predictions = self.rank(text, options).predictions

        # 步骤5: 验证和补充选项
        selected_options = [o for o in predictions if o in options]
        selected_options = selected_options + [
            o for o in options if o not in selected_options
        ]
    else:
        selected_options = options

    return dspy.Prediction(predictions=selected_options)
```

**推理-检索子流程**:

```python
def forward(self, text: str) -> dspy.Prediction:
    # 步骤1: 语言模型推理
    preds = self.infer(text).predictions  # 生成查询标签集合

    # 步骤2: 向量检索
    scores = self.retriever.retrieve(preds)  # 计算相似度分数

    # 步骤3: 先验概率调整
    scores = self._update_scores_with_prior(scores)

    # 步骤4: 按分数排序
    labels = sorted(scores, key=lambda k: scores[k], reverse=True)

    return dspy.Prediction(predictions=labels)
```

### 3. 优化过程详细分析

**Left-to-Right优化器执行流程**:

```python
def optimize(self, program, train_examples, validation_examples):
    # 第一轮: 优化推理模块
    if self.infer_compile:
        # 创建教师程序
        teacher = InferRetrieveRank(program.config)
        teacher.infer_retrieve.infer.cot.lm = self.modules_to_lms["infer_retrieve.infer"]["teacher"]
        teacher.rank.cot.lm = self.modules_to_lms["rank"]["teacher"]

        # 禁用排序模块进行第一轮优化
        rank_skipped = program.rank_skip
        teacher.rank_skip = True
        program.rank_skip = True

        # 创建编译器
        infer_compiler = BootstrapFewShotWithRandomSearch(
            metric=self.infer_compile_metric,
            max_bootstrapped_demos=2,
            max_labeled_demos=0,
            max_rounds=1,
            num_candidate_programs=10,
            num_threads=8,
        )

        # 编译推理模块
        program = infer_compiler.compile(
            program,
            teacher=teacher,
            trainset=train_examples,
            valset=validation_examples,
            restrict=range(20),  # 限制使用前20个样本
        )

        # 恢复排序模块设置
        program.rank_skip = rank_skipped
        program.infer_retrieve._compiled = True
        program._compiled = False

    # 第二轮: 优化排序模块
    if self.rank_compile and not program.rank_skip:
        # 创建第二轮教师
        teacher = program.deepcopy()
        teacher.rank.cot.lm = self.modules_to_lms["rank"]["teacher"]

        rank_compiler = BootstrapFewShotWithRandomSearch(
            metric=self.rank_compile_metric,
            max_bootstrapped_demos=2,
            max_labeled_demos=0,
            max_rounds=1,
            num_candidate_programs=10,
            num_threads=8,
        )

        # 编译排序模块
        program = rank_compiler.compile(
            program,
            teacher=teacher,
            trainset=train_examples,
            valset=validation_examples,
            restrict=range(20),
        )

    return program
```

## 配置文件详解

### 1. 语言模型配置

**lm_config.json结构**:
```json
{
    "gpt-3.5-turbo-instruct": {
        "model": "gpt-3.5-turbo-instruct",
        "api_key": "${OPENAI_API_KEY}",
        "max_tokens": 2048
    },
    "gpt-4-1106-preview": {
        "model": "gpt-4-1106-preview",
        "api_key": "${OPENAI_API_KEY}",
        "max_tokens": 4096
    },
    "llama-2-13b-chat": {
        "model": "meta-llama/Llama-2-13b-chat-hf",
        "url": "http://localhost:8080",
        "max_tokens": 2048
    },
    "qwen3-0.6b": {
        "model": "Qwen/Qwen2.5-0.5B-Instruct",
        "url": "http://localhost:8081",
        "max_tokens": 2048
    }
}
```

### 2. 数据集配置映射

**数据集参数对照表**:

| 数据集 | 推理签名 | 排序签名 | 检索模型 | 先验路径 | 本体路径 |
|--------|----------|----------|----------|----------|----------|
| esco_tech | infer_esco | rank_esco | all-mpnet-base-v2 | ./data/esco/esco_priors.json | ./data/esco/skills_en_label.txt |
| biodex_reactions | infer_biodex | rank_biodex | BioLORD-STAMB2-v1 | ./data/biodex/biodex_priors.json | ./data/biodex/reaction_terms.txt |
| math1 | infer_math1 | rank_math1 | all-mpnet-base-v2 | ./data/math1_data/math1_points_priors.json | ./data/math1_data/math1_points.txt |

## 性能调优指南

### 1. 超参数调优

**关键超参数及其影响**:

```python
# 先验概率调整强度
prior_A = 0  # 0=禁用, 1000=强调高频标签

# 排序候选数量
rank_topk = 50  # 平衡精度和效率

# 分块参数
chunk_context_window = 3000  # 文本块大小
chunk_max_windows = 5  # 最大分块数量
chunk_window_overlap = 0.02  # 重叠比例

# 优化参数
max_bootstrapped_demos = 2  # 自举演示数量
num_candidate_programs = 10  # 候选程序数量
max_rounds = 1  # 优化轮数
```

**参数调优建议**:

1. **prior_A调优**:
   - 医学领域(BioDEX): 1000 (强调高频反应)
   - 技能领域(ESCO): 0 (平等对待所有技能)
   - 数学领域(Math1): 0-1 (轻微调整)

2. **rank_topk调优**:
   - 小数据集: 20-30
   - 中等数据集: 50-100
   - 大数据集: 100-200

3. **模型选择策略**:
   - 教师模型: GPT-4 > GPT-3.5 > 本地大模型
   - 学生模型: 根据延迟要求选择
   - 检索模型: 领域特定 > 通用模型

### 2. 内存和计算优化

**GPU内存管理**:
```python
# 设备分配策略
retriever_device = "cuda:3"  # 检索模型专用GPU
lm_device = "cuda:0"  # 语言模型GPU

# 批处理大小调整
query_chunk_size = 64  # 检索批处理大小
num_threads = 8  # 评估线程数

# 缓存策略
lru_cache_size = 100000  # 检索缓存大小
embedding_cache = True  # 启用嵌入缓存
```

**计算优化技巧**:
```python
# 限制训练样本数量
restrict = range(20)  # 仅使用前20个样本进行优化

# 早停策略
max_errors = 100  # 最大错误数量

# 并行处理
DSP_NUM_THREADS = 8  # 环境变量设置线程数
```

### 3. 调试和监控

**日志配置**:
```python
# 启用详细日志
display_progress = True
display_table = 0  # 不显示详细表格

# 性能监控
import time
start_time = time.time()
# ... 执行代码
print(f"执行时间: {time.time() - start_time:.2f}秒")
```

**错误诊断**:
```python
# 检查数据加载
print(f"训练样本数: {len(train_examples)}")
print(f"验证样本数: {len(validation_examples)}")
print(f"本体大小: {len(ontology_items)}")

# 检查模型状态
print(f"推理模块已编译: {program.infer_retrieve.infer.cot._compiled}")
print(f"排序模块已编译: {program.rank.cot._compiled}")

# 检查预测质量
sample_prediction = program(validation_examples[0].text)
print(f"样本预测: {sample_prediction.predictions[:10]}")
```

## 最佳实践

### 1. 项目结构最佳实践

**推荐目录结构**:
```
xmc.dspy/
├── src/                    # 核心源代码
│   ├── data_loaders/      # 数据加载模块
│   ├── programs/          # 核心算法模块
│   ├── evaluators.py      # 评估器
│   ├── metrics.py         # 评估指标
│   ├── optimizer.py       # 优化器
│   └── utils.py          # 工具函数
├── data/                  # 数据目录
│   ├── esco/             # ESCO数据集
│   ├── biodex/           # BioDEX数据集
│   ├── math1_data/       # Math1数据集
│   └── embeddings/       # 嵌入缓存
├── scripts/              # 脚本文件
├── results/              # 实验结果
├── local_cache/          # DSPy缓存
└── requirements.txt      # 依赖列表
```

### 2. 代码开发最佳实践

**新数据集集成模板**:
```python
# 1. 创建数据加载器
def _load_new_dataset():
    """加载新数据集的标准模板"""
    # 加载原始数据
    raw_data = load_raw_data()

    # 转换为标准格式
    validation_df = prepare_dataframe(raw_data['validation'])
    test_df = prepare_dataframe(raw_data['test'])

    # 构建本体
    ontology_items = extract_ontology(raw_data)
    ontology_descriptions = extract_descriptions(raw_data)

    # 计算先验概率
    ontology_prior = calculate_priors(raw_data['train'])

    return validation_df, test_df, ontology_items, ontology_descriptions, ontology_prior

# 2. 定义领域签名
class InferSignatureNewDomain(dspy.Signature):
    """新领域的推理签名"""
    __doc__ = "任务描述"

    text = dspy.InputField(prefix="输入前缀:")
    output = dspy.OutputField(
        prefix="输出前缀:",
        desc="输出描述",
        format=lambda x: ", ".join(x) if isinstance(x, list) else x,
    )

# 3. 注册组件
supported_signatures["infer_new_domain"] = InferSignatureNewDomain
```

### 3. 实验管理最佳实践

**实验配置版本控制**:
```python
# 实验配置文件 experiment_configs.py
ESCO_TECH_CONFIG = {
    "dataset_name": "esco_tech",
    "infer_signature_name": "infer_esco",
    "rank_signature_name": "rank_esco",
    "infer_student_model_name": "llama-2-13b-chat",
    "infer_teacher_model_name": "gpt-3.5-turbo-instruct",
    "rank_student_model_name": "gpt-4-1106-preview",
    "rank_teacher_model_name": "gpt-4-1106-preview",
    "prior_A": 0,
    "rank_topk": 50,
    "optimizer_name": "left-to-right",
}

# 批量实验执行
def run_experiment_batch(configs):
    """批量执行实验"""
    results = []
    for config in configs:
        exp, program = compile_irera(**config)
        results.append({
            "config": config,
            "results": exp,
            "program_path": exp.get_name(0)
        })
    return results
```

**结果分析工具**:
```python
def analyze_results(results_dir):
    """分析实验结果"""
    import glob
    import json

    result_files = glob.glob(f"{results_dir}/*/results.json")

    analysis = {
        "best_rp10": 0,
        "best_config": None,
        "all_results": []
    }

    for file in result_files:
        with open(file, 'r') as f:
            result = json.load(f)

        if result.get('validation_rp10', 0) > analysis["best_rp10"]:
            analysis["best_rp10"] = result['validation_rp10']
            analysis["best_config"] = result

        analysis["all_results"].append(result)

    return analysis
```

### 4. 部署最佳实践

**生产环境配置**:
```python
# 生产环境优化配置
PRODUCTION_CONFIG = {
    "chunk_context_window": 2000,  # 减少内存使用
    "rank_topk": 30,              # 平衡精度和速度
    "num_threads": 4,             # 适应服务器配置
    "max_errors": 50,             # 快速失败
    "display_progress": False,    # 禁用进度显示
}

# API服务封装
class IReRaService:
    def __init__(self, model_path, config_path):
        self.program = InferRetrieveRank.load(model_path)
        self.config = json.load(open(config_path))

    def predict(self, text: str) -> dict:
        """预测接口"""
        try:
            prediction = self.program(text)
            return {
                "success": True,
                "predictions": prediction.predictions[:10],
                "confidence_scores": self._calculate_confidence(prediction)
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }

    def _calculate_confidence(self, prediction):
        """计算置信度分数"""
        # 实现置信度计算逻辑
        return [1.0] * len(prediction.predictions[:10])
```

这个详细的技术文档涵盖了IReRa项目的所有核心组件、实现细节和使用方法，为开发者提供了全面的技术参考。文档包含了从基础概念到高级优化的完整知识体系，可以帮助开发者快速理解和使用这个极端多标签分类系统。
